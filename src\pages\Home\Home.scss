.home {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #fff;
  z-index: 1;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("/images/amiya-astrakan.jpg");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    filter: brightness(0.7);
    z-index: -1;
  }

  &__banner {
    display: none;
  }

  &__content {
    max-width: 70em;
    margin: 0 auto;
    padding: 2rem;
    border-radius: 10px;

    &__title {
      font-size: 4rem;
      font-weight: 700;
      margin-bottom: 1.5rem;
      line-height: 1.2;
      letter-spacing: 2px;
      font-family: PPR;
      text-transform: lowercase;
      span {
        color: #fff;
        font-family: PPEI;
        text-transform: capitalize;
      }
    }

    &__text {
      font-size: 0.9rem;
      margin-bottom: 3rem;
      line-height: 1.2;
      max-width: 360px;
      margin-left: auto;
      margin-right: auto;
      font-family: PPR;
    }
  }

  .btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: absolute;
    bottom: 5%;
    left: 50%;
    transform: translateX(-50%);
    gap: 1em;
    width: 50em;
    max-width: 90%;
    font-family: PPR;
    font-size: 15px;
    color: #3b3b3b;
    text-transform: uppercase;
    text-decoration: underline;
    border-color: #3b3b3b;
    border-radius: 35px;
    padding: 0.4em 0.4em;
    background-color: #ffffffbc;
    cursor: pointer;
    transition: all 0.3s ease-in-out;

    .line2 {
      background-color: #3b3b3b;
      border-radius: 100%;
      padding: 0.8em;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 15px;
      position: relative;
      overflow: hidden;
      padding: 1.5em;

      span {
        position: absolute;
        transition: all 0.5s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;

        &:first-child {
          transform: translateX(0);
        }

        &:last-child {
          transform: translateX(-100%);
        }
      }
    }

    &:hover .line2 {
      scale: 1.2;
      transition: all 0.5s ease-in-out;
      span:first-child {
        transform: translateX(180%);
      }

      span:last-child {
        transform: translateX(0);
        transition-delay: 0.2s;
      }
    }

    &:not(:hover) .line2 {
      span:first-child {
        transform: translateX(0);
        transition-delay: 0.2s;
      }

      span:last-child {
        transform: translateX(-180%);
      }
    }

    & > div {
      cursor: pointer;
    }
  }
}

.sections-container {
  padding: 5em 0em;
  position: relative;
  z-index: 1;
  h2 {
    font-family: PPR;
    font-size: clamp(1.2rem, 5vw, 3em);
    text-transform: capitalize;
    font-weight: 300;
    letter-spacing: -2px;
    line-height: 1.1;
    position: relative;
    z-index: 2;
    word-break: break-word;
    overflow-wrap: break-word;
    text-align: start;
    span {
      font-family: PPEI;
    }
    br {
      display: none;
    }
  }

  .text {
    font-family: PPR;
    font-size: clamp(2.2rem, 10vw, 7em);
    text-transform: uppercase;
    font-weight: 300;
    letter-spacing: -2px;
    line-height: 1.05;
    position: relative;
    z-index: 2; // Ensure text is above any overlapping cards
    word-break: break-word;
    overflow-wrap: break-word;
    text-align: center;
  }

  .img {
    padding: 4em 0em;
    position: relative;
    z-index: 2; // Ensure images are above any overlapping cards
  }
}

// Responsive styles
@media (max-width: 1024px) {
  .sections-container {
    padding: 2.5em 0.5em;
    /* Mobile overrides removed to rely on clamp scaling */
  }
  .slider-cards {
    overflow-x: auto;
    overflow-y: visible;
    display: flex;
    gap: 18px;
    padding-bottom: 1em;
    width: 100vw;
    height: 20em;
    box-sizing: border-box;
  }
  .slider-card {
    width: 90vw;
    min-width: 220px;
    max-width: 420px;
    flex: 0 0 auto;
    background: #fff;
    border-radius: 18px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    display: flex;
    flex-direction: column;
    align-items: stretch;
    overflow: hidden;
    transition: box-shadow 0.2s;
    &:hover {
      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
    }
    &__img {
      width: 100%;
      height: 20em;
      object-fit: cover;
      object-position: top;
      display: block;
    }
    &__body {
      flex: 1 1 auto;
      padding: 1em 1em 0.7em 1em;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      gap: 0.5em;
    }
    &__title {
      font-size: 1.2em;
      font-weight: 600;
      margin-bottom: 0.2em;
      color: #222;
    }
    &__desc {
      font-size: 1em;
      color: #555;
    }
    &__price {
      font-size: 1.1em;
      color: #1a1a1a;
      font-weight: 700;
    }
  }
}

@media (max-width: 768px) {
  .home {
    &__content {
      padding: 1.2rem;
      &__title {
        font-size: 2rem;
      }
      &__text {
        font-size: 1rem;
      }
    }
  }
  .sections-container {
    padding: 1.5em 0.2em;
    .text {
      font-size: 5em;
    }
    h2 {
      font-size: 5rem;
      br {
        display: none;
      }
    }
  }
  .slider-cards {
    gap: 8px;
    width: 100vw;
    max-width: 100vw;
    height: 100%;
    display: flex;
    align-items: stretch;
  }
  .card-slide {
    display: flex;
    align-items: stretch;
    width: 100%;
  }
  .slider-card {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 100vw;
    height: 100%;
    box-sizing: border-box;
    border-radius: 18px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    background: #fff;
  }
  .slider-card__img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }
  .slider-card__body {
    flex: 1 1 auto;
    padding: 0.5em 0.3em 0.3em 0.3em;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 0.5em;
    min-width: 0;
  }
  .slider-card__title {
    font-size: 1em;
    font-weight: 600;
    margin-bottom: 0.2em;
    color: #222;
  }
  @media (max-width: 900px) {
    .slider-cards {
      gap: 4px;
    }
    .slider-card {
      aspect-ratio: 3/4;
    }
    .slider-card__img {
      aspect-ratio: 3/2;
    }
  }
  @media (max-width: 600px) {
    .slider-cards {
      gap: 2px;
    }
  }

  .arrow-btn {
    width: 36px !important;
    height: 36px !important;
    svg {
      width: 16px;
      height: 16px;
    }
  }
}

@media (max-width: 480px) {
  .home {
    &__content {
      &__title {
        font-size: 1.2rem;
      }
      &__text {
        font-size: 0.9rem;
      }
    }
    .btn {
      padding: 0.8rem 1rem;
      font-size: 0.8rem;
    }
  }
  .sections-container {
    .text {
      font-size: 3em;
    }
    h2 {
      font-size: 3rem;
    }
    img {
      object-fit: cover;
      object-position: top;
    }
  }
  .slider-card {
    &__img {
      height: 27em;
      object-fit: contain;
      object-position: top;
    }
    &__body {
      padding: 0.5em 0.3em 0.3em 0.3em;
    }
    &__title {
      font-size: 1em;
    }
  }
  .arrow-btn {
    width: 36px !important;
    height: 36px !important;
    svg {
      width: 16px;
      height: 16px;
    }
  }
}

@media (max-width: 480px) {
  .home {
    &__content {
      &__title {
        font-size: 2rem;
      }

      &__text {
        font-size: 1rem;
      }
    }

    .btn {
      padding: 0.8rem 2rem;
      font-size: 0.9rem;
    }
  }
}
