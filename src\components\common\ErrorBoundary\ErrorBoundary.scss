.error-boundary {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

  &__container {
    max-width: 600px;
    text-align: center;
    background: white;
    padding: 3rem;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  &__icon {
    color: #ef4444;
    margin-bottom: 2rem;
    
    svg {
      width: 64px;
      height: 64px;
    }
  }

  &__title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
    font-family: 'PPS', sans-serif;
  }

  &__message {
    font-size: 1.125rem;
    color: #6b7280;
    margin-bottom: 2rem;
    line-height: 1.6;
    font-family: 'PPR', sans-serif;
  }

  &__actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 2rem;
  }

  &__button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    font-family: 'PPR', sans-serif;

    &--primary {
      background-color: #000;
      color: white;

      &:hover {
        background-color: #333;
        transform: translateY(-2px);
      }
    }

    &--secondary {
      background-color: #f3f4f6;
      color: #374151;
      border: 1px solid #d1d5db;

      &:hover {
        background-color: #e5e7eb;
        transform: translateY(-2px);
      }
    }
  }

  &__details {
    text-align: left;
    margin-top: 2rem;
    padding: 1rem;
    background-color: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;

    summary {
      cursor: pointer;
      font-weight: 600;
      color: #374151;
      margin-bottom: 0.5rem;
    }
  }

  &__stack {
    background-color: #1f2937;
    color: #f9fafb;
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 0.875rem;
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-word;
  }
}

@media (max-width: 768px) {
  .error-boundary {
    padding: 1rem;

    &__container {
      padding: 2rem 1.5rem;
    }

    &__title {
      font-size: 2rem;
    }

    &__actions {
      flex-direction: column;
      align-items: center;
    }

    &__button {
      width: 100%;
      max-width: 200px;
      justify-content: center;
    }
  }
}
