.custom-alert {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  padding: 15px 25px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-family: 'PPMori-Regular', sans-serif;
  color: #fff;
  opacity: 0;
  animation: fadeIn 0.5s forwards;

  &--success {
    background-color: #4CAF50; // Green
  }

  &--error {
    background-color: #f44336; // Red
  }

  &--info {
    background-color: #2196F3; // Blue
  }

  &__content {
    display: flex;
    align-items: center;
    flex-grow: 1;

    p {
      margin: 0;
      font-size: 1em;
      line-height: 1.4;
    }
  }

  &__close-button {
    background: none;
    border: none;
    color: #fff;
    font-size: 1.5em;
    cursor: pointer;
    margin-left: 20px;
    padding: 0;
    line-height: 1;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.1);
    }
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
  to { opacity: 1; transform: translateX(-50%) translateY(0); }
}

@keyframes fadeOut {
  from { opacity: 1; transform: translateX(-50%) translateY(0); }
  to { opacity: 0; transform: translateX(-50%) translateY(-20px); }
}