import { describe, it, expect, vi } from "vitest";

// Mock fetch
global.fetch = vi.fn();

describe("Product data transformation", () => {
  const mockProduct = {
    id: 1,
    title: "Test Product",
    body_html: "Test description",
    images: [{ src: "test.jpg" }],
    variants: [
      {
        price: "100.00",
        option1: "Red",
        option2: "M",
        available: true,
      },
      {
        price: "100.00",
        option1: "Blue",
        option2: "L",
        available: true,
      },
    ],
  };

  it("should have basic product structure", () => {
    expect(mockProduct).toHaveProperty("id");
    expect(mockProduct).toHaveProperty("title");
    expect(mockProduct).toHaveProperty("variants");
    expect(mockProduct.variants).toBeInstanceOf(Array);
  });

  it("should have valid variant data", () => {
    const variant = mockProduct.variants[0];
    expect(variant).toHaveProperty("price");
    expect(variant).toHaveProperty("option1");
    expect(variant).toHaveProperty("option2");
  });
});
