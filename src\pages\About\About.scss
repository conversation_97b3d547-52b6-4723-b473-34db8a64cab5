.about-page {
  font-family: "PPMori-Regular", sans-serif;
  color: #333;
  line-height: 1.6;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;

  .about-header {
    text-align: center;
    margin-bottom: 60px;

    h1 {
      font-size: 3.5em;
      color: #000;
      margin-bottom: 15px;
      font-family: "EditorialNew-Regular", serif;
    }

    p {
      font-size: 1.2em;
      color: #555;
      max-width: 800px;
      margin: 0 auto;
    }
  }

  .about-section {
    margin-bottom: 60px;
    padding: 40px;
    background-color: #f9f9f9;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);

    h2 {
      font-size: 2.5em;
      color: #000;
      margin-bottom: 25px;
      text-align: center;
      font-family: "EditorialNew-Regular", serif;
    }

    &.team-section {
      h2 {
        margin-bottom: 40px;
      }
    }

    p {
      font-size: 1.1em;
      margin-bottom: 15px;
      text-align: justify;
    }

    ul {
      list-style: none;
      padding: 0;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;

      li {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
        font-size: 1.05em;
        strong {
          color: #000;
        }
      }
    }
  }

  .founder-section {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 60px;
    background-color: #fff;
    padding: 60px;

    .founder-content {
      flex: 1;
      h2 {
        text-align: left;
        margin-bottom: 20px;
        font-size: 2.8em;
      }
      p {
        text-align: left;
        margin-bottom: 15px;
        line-height: 1.8;
      }
    }

    .founder-image-container {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;

      .founder-image {
        max-width: 100%;
        height: auto;
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .team-section {
    .team-members-container {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 40px;
    }

    .team-member {
      background-color: #fff;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
      text-align: center;
      flex: 1;
      min-width: 280px;
      max-width: 350px;
      transition: all 0.3s ease;

      h3 {
        font-size: 1.8em;
        color: #000;
        margin-bottom: 10px;
        font-family: "EditorialNew-Regular", serif;
      }

      p {
        font-size: 1em;
        color: #666;
        margin-bottom: 5px;
      }
    }
  }

  .about-footer {
    text-align: center;
    margin-top: 60px;
    padding-top: 30px;
    border-top: 1px solid #eee;
    color: #777;
    font-size: 0.9em;
  }
}

@media (max-width: 1024px) {
  .founder-section {
    flex-direction: column;
    text-align: center;

    .founder-content {
      h2,
      p {
        text-align: center;
      }
    }
  }
}

@media (max-width: 768px) {
  .about-page {
    padding: 20px;

    .about-header {
      h1 {
        font-size: 2.5em;
      }
      p {
        font-size: 1em;
      }
    }

    .about-section {
      padding: 25px;

      h2 {
        font-size: 2em;
      }

      ul {
        grid-template-columns: 1fr;
      }
    }

    .team-section {
      flex-direction: column;
      align-items: center;
    }
  }
}
