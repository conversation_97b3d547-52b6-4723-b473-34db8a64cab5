nav {
  // Default styles for all pages
  background: #000;
  color: #fff;
  border-radius: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1em 5em;
  transition: all 0.3s ease-in-out;

  // Home page specific styles - only visual differences
  body.home-page & {
    background: transparent;
    color: #fff;
    box-shadow: none;

    // Scrolled state for home page - apply other pages styling
    &.scrolled {
      background: #fff !important;
      color: #000 !important;
      border-radius: 0;
      padding: 1em 5em;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
  }

  &.scrolled {
    background: #fff;
    color: #000;
    border-radius: 0;
    padding: 1em 5em;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .hamburger {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 38px;
    height: 38px;
    background: none;
    border: none;
    cursor: pointer;
    z-index: 1200;
    span {
      display: block;
      width: 26px;
      height: 3px;
      margin: 4px 0;
      background: currentColor;
      border-radius: 2px;
      transition: 0.4s;
    }
    &.open span:nth-child(1),
    span.open:nth-child(1) {
      transform: rotate(45deg) translate(13px, 13px);
    }
    &.open span:nth-child(2),
    span.open:nth-child(2) {
      opacity: 0;
    }
    &.open span:nth-child(3),
    span.open:nth-child(3) {
      transform: rotate(-45deg) translate(6px, -6px);
    }
  }

  .desktop-menu {
    display: flex;
  }

  .nav-btn-group {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 9999px;
    padding: 0.2em 0.4em;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    gap: 0;
  }
  .nav-btn-item {
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    border: none;
    outline: none;
    padding: 0 1.2em;
    height: 48px;
    cursor: pointer;
    color: #222;
    font-size: 1.2rem;
    position: relative;
    transition: background 0.2s;
    text-decoration: none;
  }
  .nav-btn-divider {
    width: 1px;
    height: 28px;
    background: #bbb;
    margin: 0 0.2em;
    display: block;
  }
  .nav-btn-group .icon-cart,
  .nav-btn-group .icon-account {
    display: block;
    margin: 0;
  }

  .mobile-menu {
    display: none;
  }

  .right-side-nav-items {
    display: none;
    align-items: center;
    gap: 1em;
  }

  @media (max-width: 1024px) {
    font-size: clamp(1rem, 5vw, 1.5rem);
    width: 100%;
    padding: 1em 2em;
    .logo {
      width: auto;
      height: 40px;
      position: relative;
      align-self: start;
      svg {
        width: 80px;
        height: 40px;
      }
    }
    .nav-btn-group {
      padding: 0.1em 0.1em;
      margin-left: auto;
      margin-right: 1em;
      align-items: center;
      justify-content: flex-end;
    }
    body.home-page & {
      background: transparent;
      color: #fff;
      box-shadow: none;

      // Scrolled state for home page - apply other pages styling
      &.scrolled {
        background: #fff !important;
        color: #000 !important;
        padding: 1em 2em;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
    }
    .desktop-menu {
      display: none !important;
    }

    .logo {
      z-index: 1100;
      width: 100px; /* Adjust as needed for responsive view */
      height: auto;
      svg {
        width: 100%;
        height: auto;
      }
    }

    .hamburger {
      display: flex;
      z-index: 1100;
    }

    .right-side-nav-items {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
    .mobile-nav-links {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1em;
      width: 100%;
    }
    .mobile-menu {
      display: flex;
      flex-direction: column;
      position: fixed;
      top: 0;
      right: 0;
      left: 0;
      bottom: 0;
      font-family: "PPR";
      background: #fff;
      color: #000;
      backdrop-filter: blur(10px);
      padding: 5rem 2rem 2rem;
      gap: 1.5rem;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      z-index: 1000;
      & {
        opacity: 0;
        pointer-events: none;
        transform: translateY(20px);
        transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
      }

      body.home-page & {
        background: transparent;
        color: #fff;
      }

      a {
        color: inherit;
        font-weight: 500;
        width: 50%;
        text-align: center;
        position: relative;
        opacity: 0;
        transform: translateY(10px);
        transition: all 0.3s ease;

        &::after {
          content: "";
          position: absolute;
          width: 0;
          height: 1px;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          background-color: currentColor;
          transition: width 0.3s ease;
        }

        &:hover::after {
          width: 50%;
        }
      }

      .btn {
        width: 100%;
        max-width: 200px;
        margin: 1.5rem auto 0;
        padding: 0.8rem 1.5rem;
        border-radius: 30px;
        background: #fff;
        color: #000;
        display: flex;
        gap: 1rem;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        opacity: 0;
        transform: translateY(10px);
        width: 100%;

        &:hover {
          background: #f0f0f0;
          transform: translateY(-2px);
        }
      }
    }

    .mobile-menu.open {
      opacity: 1;
      pointer-events: auto;
      transform: translateY(0);

      a,
      .btn {
        opacity: 1;
        transform: translateY(0);
        transition-delay: calc(0.1s * var(--i, 0));
      }
    }
  }

  /* Home page styles */
  &:not(.scrolled) {
    background-color: transparent;
    color: #fff;
    box-shadow: none;
  }
  .logo {
    text-decoration: none;
    color: inherit;
    font-family: "PPR";
    font-size: 1.5em;
    font-weight: 500;
    margin-right: 2em;
    margin-left: -2em;
  }

  .menu {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4em;
    font-family: "PPR";
    font-size: 15px;

    & > a {
      position: relative;
      padding: 0;

      &::after {
        content: "";
        position: absolute;
        width: 0;
        height: 1px;
        bottom: 0;
        left: 55%;
        transform: translateX(-55%);
        background-color: currentColor;
        transition: all 0.3s ease-in-out;
      }

      &:hover::after {
        width: 100%;
      }
    }
  }
  .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.4em;
    font-family: "PPR";
    font-size: 15px;
    color: #000;
    border-radius: 25px;
    padding: 0.5em 1.7em;
    background-color: #fff;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
    .line {
      width: 1px;
      height: 1.5em;
      background-color: #000;
    }
    & > div {
      cursor: pointer;
    }
  }

  a {
    color: inherit;
    text-decoration: none;
    position: relative;
    display: inline-block;
    padding: 0.5em 0;
  }
}

@media (max-width: 520px) {
  nav {
    padding: 1em 1em; /* Adjusted padding for smaller screens */

    .logo {
      width: auto;
      height: 40px;
      position: relative;
      bottom: 0.3em;
      svg {
        width: 80px;
        height: 40px;
      }
    }

    // Ensure consistent logo positioning for home page too
    body.home-page & {
      padding: 1em 1em; /* Same padding as other pages */

      .logo {
        width: auto;
        height: 40px;
        position: relative;
        align-self: start;
        margin-left: 0em;
        bottom: 0.3em;
        svg {
          width: 80px;
          height: 40px;
        }
      }
    }

    .nav-btn-group {
      padding: 0.1em 0.1em;
      margin-left: auto;
      margin-right: 1em;
      align-items: center;
      justify-content: flex-end;
    }
  }
}
