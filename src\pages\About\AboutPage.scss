.about-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh; /* Light beige background */
  padding: 20px;

  @media (max-width: 767px) {
    margin-top: 4em;
    padding: 10px;
  }
  @media (max-width: 512px) {
    margin-top: 4em;
  }

  .about-content {
    display: flex;
    flex-direction: column;
    max-width: 1200px;
    width: 100%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    overflow: hidden;

    @media (min-width: 768px) {
      flex-direction: row;
    }

    .text-section {
      flex: 1;
      padding: 40px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      color: #333;

      .founder-label {
        font-size: 0.9em;
        // color: #4f46e5; /* Sienna color */
        margin-bottom: 10px;
        text-transform: uppercase;
        letter-spacing: 2px;
      }

      .founder-name {
        font-family: "PPS";
        font-size: 2.5em; /* Adjusted for smaller screens */
        line-height: 1.1;
        margin-bottom: 20px;
        color: #333;

        @media (min-width: 768px) {
          font-size: 5em;
        }

        @media (max-width: 480px) {
          font-size: 2em;
        }
      }
    }

    .ceo-title {
      font-size: 1em;
      word-spacing: 1px;
      margin-bottom: 30px;
      text-transform: uppercase;
      letter-spacing: 1.5px;
    }

    .description {
      font-size: 1em; /* Adjusted for smaller screens */
      line-height: 1.6;
      margin-bottom: 30px;
      color: #555;

      @media (max-width: 480px) {
        font-size: 0.9em;
      }
    }
  }

  .website-link {
    // color: #4f46e5;
    text-decoration: none;
    font-weight: bold;
    width: fit-content;
    display: inline-flex;
    align-items: center;
    gap: 0.5em;
    transition: color 0.3s ease;
    position: relative;

    .arrow {
      transition: transform 0.3s ease;
    }

    &::after {
      content: "";
      position: absolute;
      width: 0;
      height: 1px;
      bottom: 0;
      left: 0;
      background-color: currentColor;
      transition: width 0.3s ease;
    }

    &:hover {
      color: #3730a3;

      .arrow {
        transform: translateX(5px);
      }

      &::after {
        width: 100%;
      }
    }
  }
}

.image-section {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }
}
