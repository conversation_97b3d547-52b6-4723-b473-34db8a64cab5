.contact-page {
  padding: 10em 20px 0em 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: "PPR";
  background-color: #fff; /* Dark background */
  color: #000; /* Light text */

  .contact-hero {
    text-align: center;
    margin-bottom: 60px;

    h1 {
      font-family: "PPS", serif;
      font-size: 4.5em; /* Larger font size */
      margin-bottom: 20px;
      color: #000; /* Light color for heading */
      line-height: 1.1;
    }

    p {
      font-size: 1.3em; /* Slightly larger font size */
      color: #6d6c6c; /* Lighter grey for paragraph */
      line-height: 1.6;
      max-width: 800px;
      margin: 0 auto;
    }
  }

  .contact-details {
    display: flex;
    justify-content: center; /* Center items */
    flex-wrap: wrap;
    margin-bottom: 80px; /* More space */
    text-align: left; /* Align text left */

    .detail-item {
      flex: 0 0 calc(33% - 40px); /* Three items per row with spacing */
      max-width: 350px; /* Max width for larger screens */
      margin: 20px; /* Consistent margin */
      padding: 40px; /* More padding */
      background-color: #fff; /* Darker background for items */
      border-radius: 12px; /* More rounded corners */
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2); /* Stronger shadow */
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-5px); /* Slight lift on hover */
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3);
      }

      h3 {
        font-family: "PPS";
        font-size: 1.8em; /* Larger heading */
        color: #000; /* Lighter color for heading */
        margin-bottom: 15px;
      }

      p {
        font-size: 1.1em;
        color: #6d6c6c; /* Lighter grey for paragraph */
        line-height: 1.6;
      }
    }
  }

  .contact-form-section {
    background-color: #fff; /* Darker background */
    padding: 60px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    margin-bottom: 80px;

    h2 {
      font-family: "PPS";
      font-size: 3.2em; /* Larger heading */
      text-align: center;
      margin-bottom: 50px;
      color: #000; /* Light color for heading */
    }

    .contact-form {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;

      .form-group {
        display: flex;
        flex-direction: column;

        &:nth-child(4) {
          /* Message textarea */
          grid-column: 1 / -1;
        }

        label {
          font-size: 1.1em; /* Slightly larger label */
          color: #000; /* Lighter grey for label */
          margin-bottom: 10px;
          font-family: "PPMori-SemiBold", sans-serif;
        }

        input[type="text"],
        input[type="email"],
        textarea {
          padding: 18px; /* More padding */
          border: 1px solid #444; /* Darker border */
          background-color: #fff; /* Darker input background */
          color: #000; /* Light text in input */
          border-radius: 8px; /* More rounded corners */
          font-size: 1em;
          font-family: "PPMori-Regular", sans-serif;
          transition: border-color 0.3s ease, box-shadow 0.3s ease;

          &:focus {
            border-color: #4f46e5; /* Blue focus border */
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25); /* Focus shadow */
            outline: none;
          }

          &.error {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
          }
        }

        .error-message {
          color: #ef4444;
          font-size: 0.875rem;
          margin-top: 0.5rem;
          display: block;
        }

        textarea {
          resize: vertical;
          min-height: 150px; /* Taller textarea */
        }
      }

      .submit-button {
        grid-column: 1 / -1;
        padding: 18px 35px; /* More padding */
        background-color: #4f46e5; /* Blue button */
        color: #fff;
        border: none;
        border-radius: 8px; /* More rounded corners */
        font-size: 1.2em; /* Larger font */
        font-family: "PPMori-SemiBold", sans-serif;
        cursor: pointer;
        transition: background-color 0.3s ease, transform 0.2s ease;

        &:hover:not(:disabled) {
          background-color: #4e46e5ed; /* Darker blue on hover */
          transform: translateY(-2px); /* Slight lift */
        }

        &:active:not(:disabled) {
          transform: translateY(0); /* Press effect */
        }

        &:disabled {
          background-color: #666;
          cursor: not-allowed;
          transform: none;
          opacity: 0.7;
        }
      }
    }
  }

  .contact-map {
    text-align: center;
    margin-bottom: 80px; /* More space */

    h2 {
      font-family: "PPS"; /* Consistent font */
      font-size: 3.2em; /* Larger heading */
      margin-bottom: 40px;
      color: #000; /* Light color for heading */
    }

    .map-placeholder {
      background-color: #2a2a2a; /* Darker background for map */
      height: 450px; /* Taller map */
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 1.8em; /* Larger text */
      color: #b0b0b0; /* Lighter grey for text */
      border-radius: 12px; /* More rounded corners */
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2); /* Stronger shadow */
    }
  }
}

@media (max-width: 768px) {
  .contact-page {
    .contact-hero {
      h1 {
        font-size: 2.5em;
      }
      p {
        font-size: 1em;
      }
    }

    .contact-details {
      flex-direction: column;
      align-items: center;

      .detail-item {
        width: 90%;
        margin: 15px 0;
      }
    }

    .contact-form-section {
      padding: 30px;

      h2 {
        font-size: 2em;
      }

      .contact-form {
        grid-template-columns: 1fr;
      }
    }

    .contact-map {
      .map-placeholder {
        height: 300px;
      }
    }
  }
}
