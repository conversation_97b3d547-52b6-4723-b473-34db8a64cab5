{"name": "amiya", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "dependencies": {"@clerk/clerk-react": "^5.36.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@reduxjs/toolkit": "^2.8.2", "@stripe/react-stripe-js": "^3.8.0", "@stripe/stripe-js": "^7.6.1", "@tailwindcss/vite": "^4.1.11", "framer-motion": "^12.23.9", "gsap": "^3.13.0", "keen-slider": "^6.8.6", "lenis": "^1.3.8", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-draggable": "^4.5.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.7.0", "react-toastify": "^11.0.5", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.7.0", "eslint": "^8.47.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "happy-dom": "^18.0.1", "jsdom": "^26.1.0", "sass": "^1.89.2", "vite": "^7.0.4", "vitest": "^3.2.4"}}