/* PPMori Font Family */
@font-face {
  font-family: "PPR";
  src: url("/fonts/PPMori-Regular.woff2") format("woff2");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "PPS";
  src: url("/fonts/PPMori-SemiBold.woff2") format("woff2");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

/* Editorial New Font Family */
@font-face {
  font-family: "PPER";
  src: url("/fonts/EditorialNew-Regular.woff2") format("woff2");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "PPEI";
  src: url("/fonts/EditorialNew-Italic.woff2") format("woff2");
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "PEI";
  src: url("/fonts/EditorialNew-Lightitalic.woff2") format("woff2");
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
  width: 100%;
  font-family: "PPR";
  scroll-behavior: smooth;

  /* Hide scrollbar for webkit browsers */
  &::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }

  /* Hide scrollbar for Firefox */
  scrollbar-width: none;
  -ms-overflow-style: none;
}

body {
  height: 100%;
  width: 100%;
  font-family: "PPR";
  overflow-x: hidden;

  /* Hide scrollbar for webkit browsers */
  &::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }

  /* Hide scrollbar for Firefox */
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* Hide scrollbars globally for all elements */
* {
  &::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }

  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* Ensure smooth scrolling for all elements */
* {
  scroll-behavior: smooth;
}

/* Page transition overlay styles */
.page-transition-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #000000 100%);
  z-index: 9998;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(-100%);
  will-change: transform;
  backdrop-filter: blur(10px);
  /* Performance optimizations */
  transform-style: preserve-3d;
  backface-visibility: hidden;
  perspective: 1000px;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at center,
      rgba(255, 255, 255, 0.1) 0%,
      transparent 70%
    );
    pointer-events: none;
  }

  .transition-content {
    text-align: center;
    color: white;
    font-family: "PPR", sans-serif;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;

    .transition-page-name {
      font-size: 4rem;
      font-weight: 700;
      letter-spacing: 0.2em;
      opacity: 0;
      transform: translateY(30px) scale(0.9);
      color: white;
      text-transform: uppercase;
      will-change: transform, opacity;
      /* Performance optimizations */
      transform-style: preserve-3d;
      backface-visibility: hidden;

      @media (max-width: 768px) {
        font-size: 3.5rem;
      }

      @media (max-width: 480px) {
        font-size: 3rem;
      }
    }

    .transition-line {
      width: 200px; /* Initial width, will be dynamically adjusted */
      height: 3px;
      background: linear-gradient(90deg, transparent, white, transparent);
      opacity: 0;
      transform: scaleX(0);
      will-change: transform, opacity;
      /* Performance optimizations */
      transform-style: preserve-3d;
      backface-visibility: hidden;

      @media (max-width: 480px) {
        height: 2px;
        width: 150px;
      }
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes expandLine {
  from {
    opacity: 0;
    transform: scaleX(0);
  }
  to {
    opacity: 1;
    transform: scaleX(1);
  }
}
