.cart-page-container {
  min-height: 100vh;
  padding: 10rem 1rem;

  @media (min-width: 640px) {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  @media (min-width: 1024px) {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.cart-content-wrapper {
  max-width: 80rem; /* Equivalent to max-w-7xl */
  margin-left: auto;
  margin-right: auto;
}

.cart-inner-wrapper {
  max-width: 42rem; /* Equivalent to max-w-2xl */
  margin-left: auto;
  margin-right: auto;

  @media (min-width: 1024px) {
    max-width: 80rem; /* Equivalent to lg:max-w-7xl */
  }
}

.cart-heading {
  font-size: 2.25rem; /* text-3xl */
  font-weight: 800; /* font-extrabold */
  letter-spacing: -0.025em; /* tracking-tight */
  color: #1a202c; /* text-gray-900 */
  margin-bottom: 2rem; /* mb-8 */

  @media (min-width: 640px) {
    font-size: 2.5rem; /* sm:text-4xl */
  }
}

.empty-cart-container {
  text-align: center;
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.empty-cart-icon {
  margin-left: auto;
  margin-right: auto;
  height: 3rem;
  width: 3rem;
  color: #9ca3af; /* text-gray-400 */
}

.empty-cart-title {
  margin-top: 0.5rem;
  font-size: 0.875rem; /* text-sm */
  font-weight: 500; /* font-medium */
  color: #1a202c; /* text-gray-900 */
}

.empty-cart-text {
  margin-top: 0.25rem;
  font-size: 0.875rem; /* text-sm */
  color: #6b7280; /* text-gray-500 */
}

.empty-cart-button-wrapper {
  margin-top: 1.5rem;
}

.continue-shopping-button {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  font-size: 0.875rem; /* text-sm */
  font-weight: 500; /* font-medium */
  color: #ffffff; /* text-white */
  background-color: #4f46e5; /* bg-indigo-600 */

  &:hover {
    background-color: #4338ca; /* hover:bg-indigo-700 */
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.5),
      0 0 0 2px rgba(79, 70, 229, 0.5); /* focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 */
  }
}

.cart-main-content {
  margin-top: 3rem;

  @media (min-width: 1024px) {
    display: grid;
    grid-template-columns: repeat(12, minmax(0, 1fr));
    gap: 3rem; /* gap-x-12 */
    align-items: flex-start;

    @media (min-width: 1280px) {
      gap: 4rem; /* xl:gap-x-16 */
    }
  }
}

.cart-items-section {
  @media (min-width: 1024px) {
    grid-column: span 7 / span 7;
  }
}

.cart-item-list {
  border-top: 1px solid #e5e7eb; /* border-t border-gray-200 */
  border-bottom: 1px solid #e5e7eb; /* border-b border-gray-200 */
  & > * + * {
    border-top: 1px solid #e5e7eb; /* divide-y divide-gray-200 */
  }
}

.cart-item {
  display: flex;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;

  @media (min-width: 640px) {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }
}

.cart-item-image-wrapper {
  flex-shrink: 0;
}

.cart-item-image {
  height: 6rem; /* h-24 */
  width: 6rem; /* w-24 */
  border-radius: 0.375rem; /* rounded-md */
  object-fit: cover;
  object-position: top;

  @media (min-width: 640px) {
    height: 8rem; /* sm:h-32 */
    width: 8rem; /* sm:w-32 */
  }
}

.cart-item-details {
  margin-left: 1rem; /* ml-4 */
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  @media (min-width: 640px) {
    margin-left: 1.5rem; /* sm:ml-6 */
  }
}

.cart-item-info {
  position: relative;
  padding-right: 2.25rem; /* pr-9 */

  @media (min-width: 640px) {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1.5rem; /* gap-x-6 */
    padding-right: 0; /* sm:pr-0 */
  }
}

.cart-item-header {
  display: flex;
  justify-content: space-between;
}

.cart-item-name,
.cart-item-size,
.cart-item-color {
  font-size: 0.875rem;
  color: #4a5568; /* text-sm */
}

.cart-item-link {
  font-weight: 500; /* font-medium */
  color: #4a5568; /* text-gray-700 */

  &:hover {
    color: #4338ca; /* hover:text-gray-800 */
  }
}

.cart-item-category {
  margin-top: 0.25rem; /* mt-1 */
  font-size: 0.875rem; /* text-sm */
  color: #6b7280; /* text-gray-500 */
}

.cart-item-price {
  margin-top: 0.25rem; /* mt-1 */
  font-size: 0.875rem; /* text-sm */
  font-weight: 500; /* font-medium */
  color: #1a202c; /* text-gray-900 */
}

.cart-item-actions {
  margin-top: 1rem; /* mt-4 */

  @media (min-width: 640px) {
    margin-top: 0; /* sm:mt-0 */
    padding-right: 2.25rem; /* sm:pr-9 */
  }
}

.cart-item-quantity-controls {
  display: flex;
  align-items: center;
}

.quantity-button {
  padding: 0.25rem; /* p-1 */
  color: #9ca3af; /* text-gray-400 */

  &:hover {
    color: #4338ca; /* hover:text-gray-500 */
  }
}

.quantity-display {
  margin-left: 0.5rem; /* mx-2 */
  margin-right: 0.5rem; /* mx-2 */
  font-size: 0.875rem; /* text-sm */
}

.quantity-icon {
  height: 1rem; /* h-4 */
  width: 1rem; /* w-4 */
}

.cart-item-remove-button-wrapper {
  position: absolute;
  top: 0;
  right: 0;
}

.remove-item-button {
  margin: -0.5rem; /* -m-2 */
  padding: 0.5rem; /* p-2 */
  display: inline-flex;
  color: #9ca3af; /* text-gray-400 */

  &:hover {
    color: #eb4e4e; /* hover:text-gray-500 */
  }
}

.remove-icon {
  height: 1.25rem; /* h-5 */
  width: 1.25rem; /* w-5 */
}

.order-summary-section {
  margin-top: 4rem; /* mt-16 */
  background-color: #f9fafb; /* bg-gray-50 */
  border-radius: 0.5rem; /* rounded-lg */
  padding: 1.5rem 1rem; /* px-4 py-6 */

  @media (min-width: 640px) {
    padding: 1.5rem; /* sm:p-6 */
  }

  @media (min-width: 1024px) {
    padding: 2rem; /* lg:p-8 */
    margin-top: 0; /* lg:mt-0 */
    grid-column: span 5 / span 5;
  }
}

.order-summary-heading {
  font-size: 1.125rem; /* text-lg */
  font-weight: 500; /* font-medium */
  color: #1a202c; /* text-gray-900 */
}

.order-summary-list {
  margin-top: 1.5rem; /* mt-6 */
  display: flex;
  flex-direction: column;
  gap: 1rem; /* space-y-4 */
}

.order-summary-item {
  display: flex;
  align-items: center;
  justify-content: space-between;

  &.border-top {
    border-top: 1px solid #e5e7eb; /* border-t border-gray-200 */
    padding-top: 1rem; /* pt-4 */
  }
}

.order-summary-label {
  font-size: 0.875rem; /* text-sm */
  color: #4a5568; /* text-gray-600 */

  &.flex-align-center {
    display: flex;
    align-items: center;
  }
}

.order-summary-value {
  font-size: 0.875rem; /* text-sm */
  font-weight: 500; /* font-medium */
  color: #1a202c; /* text-gray-900 */
}

.order-total-label {
  font-size: 1rem; /* text-base */
  font-weight: 500; /* font-medium */
  color: #1a202c; /* text-gray-900 */
}

.order-total-value {
  font-size: 1rem; /* text-base */
  font-weight: 500; /* font-medium */
  color: #1a202c; /* text-gray-900 */
}

.checkout-button-wrapper {
  margin-top: 1.5rem; /* mt-6 */
}

.checkout-button {
  width: 100%; /* w-full */
  border-radius: 0.375rem; /* rounded-md */
  border: 1px solid transparent;
  background-color: #4f46e5; /* bg-indigo-600 */
  padding: 0.75rem 1rem; /* px-4 py-3 */
  font-size: 1rem; /* text-base */
  font-weight: 500; /* font-medium */
  color: #ffffff; /* text-white */
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); /* shadow-sm */

  &:hover {
    background-color: #4338ca; /* hover:bg-indigo-700 */
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.5),
      0 0 0 2px rgba(79, 70, 229, 0.5); /* focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 */
  }
}
