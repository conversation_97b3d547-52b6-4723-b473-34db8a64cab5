.deatils {
  margin: 6em 0em;
  padding: 2em;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.product-details-accordion {
  .accordion-item {
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 1rem;
    margin-bottom: 1rem;

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }

    button {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      padding: 0.5rem 0;
      text-align: left;
      font-weight: 500;
      color: #1f2937;
      background: none;
      border: none;
      cursor: pointer;
      transition: color 0.2s ease;

      &:hover {
        color: #4f46e5;
      }

      span:last-child {
        font-size: 1.125rem; /* text-lg */
      }
    }

    .accordion-content {
      padding-top: 0.5rem;
      padding-bottom: 0.25rem;

      p {
        color: #4b5563;
      }
    }
  }
}

.product-options {
  margin: 2em 0;
  padding: 1em;
}

.size {
  padding: 0.3em 0.7em;
}

.quantity-selector {
  display: flex;
  align-items: center;
  border: 1px solid #ccc;
  border-radius: 5px;
  width: fit-content;

  button {
    background-color: transparent;
    border: none;
    padding: 0.4em 1em;
    cursor: pointer;
    font-size: 1.2em;
    color: #333;

    &:hover {
      background-color: #4f46e5;
      color: #fff;
    }

    &:disabled {
      color: #ccc;
      cursor: not-allowed;
    }
  }

  span {
    padding: 0.5em 1em;
    font-size: 1.1em;
    font-weight: bold;
  }
}

.product-thumbnail-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
  margin-top: 1rem;

  .thumbnail-button {
    aspect-ratio: 1 / 1;
    overflow: hidden;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    cursor: pointer;
    border: 1px solid transparent;

    &.active {
      border-color: #4f46e5; /* ring-indigo-600 */
      box-shadow: 0 0 0 2px #4f46e5;
    }

    &:not(.active) {
      border-color: #d1d5db; /* ring-gray-300 */

      &:hover {
        border-color: #a5b4fc; /* hover:ring-indigo-300 */
      }
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: top;
    }

    &:hover {
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 1em;

  @media (min-width: 640px) {
    flex-direction: row;
  }

  button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5em;
    padding: 0.8em 1.5em;
    border-radius: 0.5em;
    font-weight: 500;
    transition: all 0.3s ease;

    &.add-to-cart {
      position: relative;
      background-color: #4f46e5;
      color: #fff;
      box-shadow: 0 8px 20px -8px rgba(79, 70, 229, 0.3);
      overflow: hidden;

      &:hover {
        background-color: #4338ca;
      }

      &:disabled {
        background-color: #e5e7eb;
        color: #9ca3af;
        cursor: not-allowed;
        box-shadow: none;
      }

      .added-to-cart-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #22c55e;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5em;
        color: #fff;
        border-radius: 0.5em;
        z-index: 10;
      }

      .ripple-effect {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 20px;
        height: 20px;
        pointer-events: none;
        border-radius: 50%;
        background: radial-gradient(
          circle,
          rgba(255, 255, 255, 0.7) 0%,
          rgba(255, 255, 255, 0) 70%
        );
        z-index: 5;
      }
    }

    &.wishlist {
      border: 1px solid #d1d5db;
      color: #374151;

      &:hover {
        border-color: #4f46e5;
        color: #4f46e5;
      }
    }
  }
}
