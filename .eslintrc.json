{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 12, "sourceType": "module"}, "plugins": ["react", "react-hooks"], "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}]}, "settings": {"react": {"version": "detect"}}}