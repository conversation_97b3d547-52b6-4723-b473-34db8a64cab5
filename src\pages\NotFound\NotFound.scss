.not-found-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-family: "PPR", sans-serif;
  overflow: hidden;
  padding: 2rem 1rem;

  @media (max-width: 768px) {
    padding: 1rem 0.5rem;
    margin-top: 5rem;
  }
}

.background-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;

  .floating-shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.02));
    
    &.shape-1 {
      width: 120px;
      height: 120px;
      top: 15%;
      left: 10%;
    }
    
    &.shape-2 {
      width: 80px;
      height: 80px;
      top: 60%;
      right: 15%;
    }
    
    &.shape-3 {
      width: 100px;
      height: 100px;
      bottom: 20%;
      left: 20%;
    }

    @media (max-width: 768px) {
      &.shape-1 {
        width: 80px;
        height: 80px;
      }
      
      &.shape-2 {
        width: 60px;
        height: 60px;
      }
      
      &.shape-3 {
        width: 70px;
        height: 70px;
      }
    }
  }
}

.not-found-container {
  width: 100%;
  max-width: 800px;
  text-align: center;
  position: relative;
  z-index: 2;

  @media (max-width: 768px) {
    max-width: 100%;
    padding: 0 1rem;
  }
}

.not-found-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 4rem 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #000000 0%, #333333 100%);
    border-radius: 24px 24px 0 0;
  }

  @media (max-width: 768px) {
    padding: 3rem 2rem;
    border-radius: 20px;
  }

  @media (max-width: 480px) {
    padding: 2rem 1.5rem;
    border-radius: 16px;
  }
}

.error-number {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  font-weight: 800;
  font-size: 8rem;
  line-height: 1;

  @media (max-width: 768px) {
    font-size: 6rem;
    gap: 0.5rem;
  }

  @media (max-width: 480px) {
    font-size: 4rem;
    margin-bottom: 1.5rem;
  }

  .four {
    color: #000000;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  }

  .zero {
    color: #666666;
    transform: scale(1.1);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.error-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
  line-height: 1.2;

  @media (max-width: 768px) {
    font-size: 2rem;
  }

  @media (max-width: 480px) {
    font-size: 1.75rem;
    margin-bottom: 0.75rem;
  }
}

.error-description {
  font-size: 1.125rem;
  color: #6b7280;
  margin-bottom: 3rem;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    font-size: 1rem;
    margin-bottom: 2.5rem;
  }

  @media (max-width: 480px) {
    font-size: 0.95rem;
    margin-bottom: 2rem;
  }
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 3rem;

  @media (max-width: 480px) {
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 2rem;
  }

  .btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    min-width: 160px;
    justify-content: center;

    @media (max-width: 480px) {
      padding: 0.875rem 1.5rem;
      font-size: 0.95rem;
      min-width: auto;
      width: 100%;
    }

    &.btn-primary {
      background: linear-gradient(135deg, #000000 0%, #333333 100%);
      color: white;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
      }
    }

    &.btn-secondary {
      background: #ffffff;
      color: #000000;
      border: 2px solid #e5e7eb;

      &:hover {
        background: #f9fafb;
        border-color: #000000;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.quick-links {
  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 1rem;

    @media (max-width: 480px) {
      font-size: 1.125rem;
      margin-bottom: 0.75rem;
    }
  }

  .links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.75rem;
    max-width: 500px;
    margin: 0 auto;

    @media (max-width: 480px) {
      grid-template-columns: 1fr;
      gap: 0.5rem;
    }
  }

  .quick-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    color: #6b7280;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;

    &:hover {
      background: #ffffff;
      border-color: #000000;
      color: #000000;
      transform: translateY(-1px);
    }

    @media (max-width: 480px) {
      padding: 0.625rem 0.875rem;
      font-size: 0.85rem;
    }
  }
}

.decorative-elements {
  position: absolute;
  bottom: -2rem;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: none;

  .pattern-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 1rem;

    @media (max-width: 768px) {
      grid-template-columns: repeat(4, 1fr);
      gap: 0.75rem;
    }

    @media (max-width: 480px) {
      grid-template-columns: repeat(3, 1fr);
      gap: 0.5rem;
    }

    .pattern-dot {
      width: 8px;
      height: 8px;
      background: #d1d5db;
      border-radius: 50%;

      @media (max-width: 480px) {
        width: 6px;
        height: 6px;
      }
    }
  }
}
