# Amiya Fashion E-commerce Web App

A modern, visually engaging, and fully functional e-commerce web application for the Amiya fashion brand. Built with React, Vite, Redux Toolkit, Clerk authentication, Stripe integration, and beautiful animations using GSAP and Framer Motion.

## Features

- **Home Page:** Animated hero, product sliders, and parallax effects for shirts and pants collections.
- **Shop:** Browse, search, filter, and sort products. Add to cart and view product details.
- **Cart:** View, update, and remove items. Cart state is persisted in localStorage.
- **Checkout:** Enter shipping and payment details. Simulated order placement with summary.
- **Authentication:** Sign in/up with Clerk, including SSO support.
- **About:** Meet the founder and learn about the brand.
- **Contact:** Animated contact form with validation and toast notifications.
- **Responsive Design:** Fully responsive and mobile-friendly.
- **Page Transitions:** Smooth transitions and loader animations.
- **404 Page:** Custom animated not found page with quick links.

## Tech Stack

- **Frontend:** React 19, Vite
- **State Management:** Redux Toolkit
- **Routing:** React Router DOM
- **Authentication:** Clerk
- **Payments:** Stripe (UI only, no real transactions)
- **Animations:** GSAP, Framer Motion
- **UI/UX:** SCSS, Tailwind CSS, Keen Slider, Lucide React Icons
- **Notifications:** React Toastify

## Project Structure

- `src/pages/` — Main pages (Home, Shop, About, Contact, Cart, Checkout, NotFound)
- `src/components/` — Reusable UI components (Navbar, Footer, Loader, Cards, etc.)
- `src/features/` — Redux slices and API logic
- `src/routers/` — App routing and transitions
- `public/` — Static assets (images, fonts)

## Getting Started

### Prerequisites

- Node.js (v18+ recommended)
- npm or yarn

### Installation

```bash
git clone https://github.com/pranjalkuhikar/Amiya.git
cd Amiya
npm install
# or
yarn install
```

### Running Locally

```bash
npm run dev
# or
yarn dev
```

The app will be available at `http://localhost:5173` (or as shown in your terminal).

### Building for Production

```bash
npm run build
# or
yarn build
```

### Linting

```bash
npm run lint
# or
yarn lint
```

## Environment Variables

Create a `.env` file and add your Clerk publishable key:

```
VITE_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
```

## Deployment

This project is ready for deployment on Vercel (see `vercel.json`).

## Credits

- [Clerk](https://clerk.com/) for authentication
- [GSAP](https://greensock.com/gsap/) and [Framer Motion](https://www.framer.com/motion/) for animations
- [Redux Toolkit](https://redux-toolkit.js.org/)
- [Keen Slider](https://keen-slider.io/)
- [Lucide Icons](https://lucide.dev/)

## License

MIT
