.category-page {
  background-color: #fff;
  padding: 1em 0 4em 0;
  font-family: "PPR" !important;
  color: #000;
  .heading {
    color: #000;
    font-family: "PPS";
  }
}

.products {
  padding: 0em 1em;
}

.search-container {
  margin-top: 1rem;
  margin-bottom: 1.5rem;
  margin-left: 2.5rem;
  margin-right: 2.5rem;

  .search-wrapper {
    position: relative;
    max-width: 28rem;
    margin: 0 auto;

    .search-icon {
      position: absolute;
      top: 50%;
      left: 0.75rem;
      transform: translateY(-50%);
      color: #9ca3af;
      pointer-events: none;
    }

    .search-input {
      display: block;
      width: 100%;
      padding: 0.75rem 0.75rem 0.75rem 2.5rem;
      border: 1px solid #d1d5db;
      border-radius: 0.5rem;
      background-color: #fff;
      color: #111827;
      font-size: 0.875rem;
      line-height: 1.25rem;
      transition: all 0.15s ease-in-out;

      &::placeholder {
        color: #6b7280;
      }

      &:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 1px #4f46e5;
      }

      &.has-clear-button {
        padding-right: 2.5rem;
      }
    }

    .clear-button {
      position: absolute;
      top: 50%;
      right: 0.75rem;
      transform: translateY(-50%);
      background: none;
      border: none;
      cursor: pointer;
      color: #9ca3af;
      transition: color 0.15s ease-in-out;

      &:hover {
        color: #6b7280;
      }
    }
  }
}

.mobile-filter-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  margin: 5px 10px;
  border: 1px solid #d1d5db;
  border-radius: 9999px;
  transition: all 0.3s ease;
  cursor: pointer;
  background-color: #fff;
  color: #374151;

  &:hover {
    border-color: #4f46e5;
    color: #4f46e5;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
  }
}

// Loading and Error States
.loading-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;

  .loading-spinner {
    width: 4rem;
    height: 4rem;
    border-top: 4px solid #4f46e5;
    border-right: 4px solid transparent;
    border-radius: 50%;
  }
}

.error-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;

  .error-content {
    text-align: center;
    max-width: 28rem;
    margin: 0 auto;
    padding: 1.5rem;

    .error-icon {
      width: 4rem;
      height: 4rem;
      margin: 0 auto 1rem;
      background-color: #fef2f2;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #dc2626;
    }

    .error-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #111827;
      margin-bottom: 0.5rem;
    }

    .error-message {
      color: #6b7280;
      margin-bottom: 1.5rem;
    }

    .retry-button {
      background-color: #4f46e5;
      color: white;
      padding: 0.75rem 1.5rem;
      border-radius: 0.5rem;
      border: none;
      cursor: pointer;
      transition: background-color 0.15s ease-in-out;

      &:hover {
        background-color: #4338ca;
      }
    }
  }
}

// Main Layout
.main-container {
  min-height: 100vh;
  background-color: #fff;
  padding-top: 5rem;
  font-family: "PPR";
}

.content-wrapper {
  margin: 0 auto;
  width: 100%;
  padding: 0 1rem 4rem;

  @media (min-width: 640px) {
    padding: 0 1.5rem 4rem;
  }

  @media (min-width: 1024px) {
    padding: 0 2rem 4rem;
  }
}

.layout-container {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;

  @media (min-width: 1024px) {
    flex-direction: row;
  }
}

.latest {
  padding: 10px;

  select {
    background-color: #ffffff;
    border: 1px solid #ccc;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    &:hover {
      border-color: #888;
    }
    &:focus {
      outline: none;
      border-color: #4f46e5;
      box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
    }
  }
}

.filterbar {
  padding: 1em;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.75rem; /* 12px */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  .price {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    padding: 15px;
  }

  .price-filter-button {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 8px 12px;
    text-align: center;
    font-size: 14px;
    cursor: pointer;
    color: #000;
    transition: all 0.2s ease-in-out;

    &:hover {
      border-color: #4f46e5;
      color: #000;
    }

    &:focus {
      outline: none;
      border-color: #4f46e5;
      color: #fff;
      background-color: #4f46e5;
      box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
    }
  }

  &.active {
    background-color: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem; /* 12px */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
    cursor: pointer;
  }
}
@media screen and (max-width: 1064px) {
  .filterbar {
    margin: 1em;
  }
}

.clear {
  background-color: #4f46e5;
  border: 1px solid #ccc;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  &:hover {
    border-color: #888;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2), 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.pagination-controls {
  .pagination-button {
    padding: 8px 16px; /* px-4 py-2 */
    margin: 0 4px; /* mx-1 */
    border-radius: 6px; /* rounded-md */
    background-color: #e5e7eb; /* bg-gray-200 */
    color: #374151; /* text-gray-700 */
    transition: all 0.2s ease-in-out;
    cursor: pointer;

    &:hover {
      border-color: #4f46e5;
      color: #4f46e5;
    }

    &.active {
      background-color: #4f46e5;
      color: #fff;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

.page {
  padding: 2em;
}

// Hero Section Styles
.hero-section {
  position: relative;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hero-content {
  text-align: center;
  z-index: 10;
  padding: 0 1rem;
}

.hero-title {
  font-size: 2.25rem;
  font-weight: 300;
  letter-spacing: -0.025em;
  margin-bottom: 1.5rem;
  font-family: "PPR";
  padding-top: 4rem;

  @media (min-width: 768px) {
    font-size: 6rem;
    padding-top: 6rem;
  }
}

.hero-description {
  color: #111827;
  max-width: 36rem;
  margin: 0 auto;
  font-size: 1.125rem;
}

.hero-cta {
  margin-top: 2rem;
}

.hero-background {
  position: absolute;
  inset: 0;
  opacity: 0.05;

  .hero-gradient {
    position: absolute;
    inset: 0;
    background: linear-gradient(to right, #e0e7ff, #f3e8ff);
  }

  .hero-pattern {
    position: absolute;
    inset: 0;
    background-image: radial-gradient(circle, #000 1px, transparent 1px);
    background-size: 20px 20px;
  }
}

// Mobile Filter Button Container
.mobile-filter-container {
  margin-bottom: 2rem;
  margin-left: 2.5rem;
  margin-right: 2.5rem;

  @media (min-width: 1024px) {
    display: none;
  }
}

// Enhanced Filter Bar
.enhanced-filterbar {
  position: sticky;
  top: 6rem;
  background-color: white;
  padding: 2rem;
  border: 1px solid #f3f4f6;
  border-radius: 1rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(8px);
}

// Price Filter Enhancements
.price-filter-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.enhanced-price-filter-button {
  width: 100%;
  text-align: left;
  padding: 0.625rem 2rem;
  font-size: 0.875rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: none;

  &.active {
    background-color: #4f46e5;
    color: white;
  }

  &.inactive {
    color: #374151;
    background-color: #f9fafb;

    &:hover {
      background-color: #f3f4f6;
    }
  }
}

// No Products State
.no-products-state {
  text-align: center;
  padding: 2rem 1rem;
  width: 100%;
  gap: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-left: 0; // Centered on mobile
  margin-top: 3rem; // Match pagination margin-top
  margin-bottom: 2rem; // Match pagination margin-bottom

  @media (min-width: 768px) {
    padding: 2rem;
    margin-left: 19em; // Offset to align with main content on desktop
  }

  .no-products-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0.75rem;
    font-family: "PPR";

    @media (min-width: 768px) {
      font-size: 2rem;
    }
  }

  .no-products-message {
    color: #6b7280;
    margin-bottom: 2rem;
    font-size: 1rem;
    line-height: 1.6;
    max-width: 400px;

    @media (min-width: 768px) {
      font-size: 1.125rem;
      max-width: 500px;
    }
  }

  .clear-all-filters-button {
    padding: 1rem 2rem;
    background-color: #4f46e5;
    color: white;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1rem;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 4px 6px -1px rgba(79, 70, 229, 0.3);

    @media (min-width: 768px) {
      padding: 1.125rem 2.5rem;
      font-size: 1.125rem;
    }

    &:hover {
      background-color: #4338ca;
      transform: translateY(-1px);
      box-shadow: 0 6px 8px -1px rgba(79, 70, 229, 0.4);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

// Products Container styles are defined within .products-grid below

// Enhanced Product Card Styles
.product-card {
  width: 100%;
  font-family: "PPR";
  transition: transform 0.3s ease-in-out;

  &:hover {
    .product-overlay {
      opacity: 1;
      background-color: rgba(0, 0, 0, 0.1);

      .overlay-text {
        transform: translateY(0);
      }
    }

    .product-image {
      transform: scale(1.05);
    }

    .product-actions {
      opacity: 1;
    }
  }
}

.product-card-container {
  position: relative;
  background-color: white;
  overflow: hidden;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease-in-out;
  isolation: isolate; // Prevents blur overflow issues

  &:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-2px);
  }
}

.product-image-container {
  width: 100%;
  aspect-ratio: 4/5;
  background-color: #f9fafb;
  overflow: hidden;
  position: relative;
  border-radius: 1rem 1rem 0 0; // Only top corners rounded

  @media (min-width: 768px) {
    aspect-ratio: 3/4;
  }
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.6s ease-in-out;
}

.product-overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease-in-out;
  z-index: 10;
  border-radius: 1rem; // Match container border radius

  .overlay-text {
    padding: 0.75rem 1.5rem;
    background-color: rgba(255, 255, 255, 0.95);
    color: #111827;
    border-radius: 1.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    transform: translateY(1rem);
    transition: all 0.3s ease-in-out;
    box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px); // Safari support

    @media (min-width: 768px) {
      padding: 0.875rem 1.75rem;
      font-size: 1rem;
    }
  }
}

.product-info {
  padding: 1.25rem 1rem 1rem 1rem;

  @media (min-width: 768px) {
    padding: 1.5rem 1.25rem 1.25rem 1.25rem;
  }
}

.product-link {
  display: block;
  text-decoration: none;
  transition: color 0.15s ease-in-out;

  &:hover {
    color: #4f46e5;
  }

  &:focus {
    outline: none;
    color: #4f46e5;
  }
}

.product-name {
  font-size: 1rem;
  font-family: "PPR";
  font-weight: 400;
  color: #111827;
  margin-bottom: 0.5rem;
  line-height: 1.4;

  @media (min-width: 768px) {
    font-size: 1.125rem;
    margin-bottom: 0.75rem;
  }
}

.product-price-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;

  @media (min-width: 768px) {
    margin-bottom: 1.25rem;
  }
}

.product-price {
  font-size: 1.125rem;
  font-family: "PPR";
  font-weight: 600;
  color: #111827;

  @media (min-width: 768px) {
    font-size: 1.25rem;
  }
}

.product-original-price {
  font-size: 1rem;
  color: #9ca3af;
  text-decoration: line-through;
  font-weight: 400;

  @media (min-width: 768px) {
    font-size: 1.125rem;
  }
}

.product-actions {
  display: flex;
  gap: 0.75rem;
  opacity: 0.8;
  transition: opacity 0.3s ease-in-out;

  @media (min-width: 768px) {
    gap: 1rem;
  }

  @media (max-width: 767px) {
    opacity: 1; // Always visible on mobile
  }
}

// Enhanced Add to Cart Button
.add-to-cart-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: #4f46e5;
  color: white;
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  font-size: 0.875rem;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 4px 6px -1px rgba(79, 70, 229, 0.3);

  @media (min-width: 768px) {
    padding: 0.875rem 1.25rem;
    font-size: 1rem;
    border-radius: 0.875rem;
  }

  &:hover {
    background-color: #4338ca;
    transform: translateY(-1px);
    box-shadow: 0 6px 8px -1px rgba(79, 70, 229, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
}

.cart-button-success-overlay {
  position: absolute;
  inset: 0;
  background-color: #10b981;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: inherit;
}

.cart-button-success-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-weight: 600;
}

.wishlist-button {
  width: 2.75rem;
  height: 2.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.75rem;
  border: 1px solid #e5e7eb;
  color: #6b7280;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);

  @media (min-width: 768px) {
    width: 3rem;
    height: 3rem;
    border-radius: 0.875rem;
  }

  &:hover {
    border-color: #a5b4fc;
    color: #4f46e5;
    background-color: #f8fafc;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
  }
}

// Products Header
.products-header-content {
  color: #111827;
  font-family: "PPR";
  font-weight: 300;
}

// Enhanced Pagination Styles
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 3rem;
  margin-bottom: 2rem;
  gap: 0.5rem;
  flex-wrap: wrap;
  padding: 0 1rem;

  @media (max-width: 640px) {
    gap: 0.25rem;
    margin-top: 2rem;
  }
}

.pagination-button {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
  height: 2.5rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background-color: white;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  user-select: none;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);

  &:hover:not(:disabled) {
    background-color: #f9fafb;
    border-color: #d1d5db;
    color: #374151;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  }

  &.active {
    background-color: #4f46e5;
    border-color: #4f46e5;
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 6px -1px rgba(79, 70, 229, 0.3),
      0 2px 4px -1px rgba(79, 70, 229, 0.2);

    &:hover {
      background-color: #4338ca;
      border-color: #4338ca;
      transform: translateY(-1px);
    }
  }

  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background-color: #f9fafb;
    color: #9ca3af;
    border-color: #e5e7eb;

    &:hover {
      transform: none;
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }
  }

  // Previous/Next button specific styles
  &.nav-button {
    padding: 0.5rem 1rem;
    font-weight: 500;

    @media (max-width: 640px) {
      padding: 0.5rem 0.75rem;
      font-size: 0.8rem;
    }
  }

  // Mobile responsive adjustments
  @media (max-width: 640px) {
    min-width: 2rem;
    height: 2rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
  }
}

// Pagination info text (optional enhancement)
.pagination-info {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1rem;
  color: #6b7280;
  font-size: 0.875rem;

  @media (max-width: 640px) {
    font-size: 0.8rem;
    margin-top: 0.75rem;
  }
}

// Additional Component Styles
.filter-sidebar {
  width: 100%;
  flex-shrink: 0;

  @media (min-width: 1024px) {
    width: 18rem;
  }
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;

  .filter-title {
    font-weight: 500;
    display: flex;
    gap: 0.5rem;
    font-size: 1.125rem;
  }

  .clear-all-button {
    font-size: 0.875rem;
    color: #4f46e5;
    font-weight: 500;
    background: none;
    border: none;
    cursor: pointer;
    transition: color 0.15s ease-in-out;

    &:hover {
      color: #4338ca;
    }
  }
}

.filter-section {
  margin-bottom: 2rem;

  .filter-section-title {
    font-weight: 500;
    margin-bottom: 1rem;
    color: #111827;
  }
}

.size-filter-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
}

.size-filter-button {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  border: 1px solid;
  text-align: center;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  background: none;

  &.active {
    border-color: #4f46e5;
    background-color: #4f46e5;
    color: white;
  }

  &.inactive {
    border-color: #e5e7eb;
    color: #374151;
    background-color: #f9fafb;

    &:hover {
      border-color: #a5b4fc;
    }
  }
}

.color-filter-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 0.75rem;
}

.color-filter-button {
  position: relative;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  border: 2px solid;
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  &.active {
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px #c7d2fe;
  }

  &.inactive {
    border-color: transparent;

    &:hover {
      border-color: #d1d5db;
    }
  }
}

.no-products-container {
  text-align: center;
  padding: 1rem 4rem;
  height: 100%;
  width: 100%;
  gap: 1.25rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .no-products-title {
    font-size: 1.25rem;
    font-weight: 500;
    color: #111827;
    margin-bottom: 0.5rem;
  }

  .no-products-message {
    color: #6b7280;
    margin-bottom: 1.5rem;
  }

  .clear-filters-button {
    padding: 0.625rem 1.5rem;
    background-color: #4f46e5;
    color: white;
    border-radius: 9999px;
    border: none;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out;

    &:hover {
      background-color: #4338ca;
    }
  }
}

.products-grid {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 1rem;

  .products-header {
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0rem;
    margin-top: 0rem;

    .sort-select {
      padding: 0.5rem 1rem;
      border: 1px solid #e5e7eb;
      border-radius: 0.5rem;
      background-color: white;
      font-size: 0.875rem;
      color: #374151;
      cursor: pointer;
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      transition: all 0.2s ease-in-out;

      &:hover {
        border-color: #4f46e5;
      }

      &:focus {
        border-color: #4338ca;
        box-shadow: 0 0 0 2px #c7d2fe;
        outline: none;
      }
    }
  }

  .products-container {
    padding: 2rem 0;
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;

    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 2.5rem;
    }
  }
}
