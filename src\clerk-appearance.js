export const clerkAppearance = {
  elements: {
    fontFamily: "PPR",
    formButtonPrimary: {
      backgroundColor: "#000000",
      "&:hover": {
        backgroundColor: "#333333",
      },
    },
    card: {
      boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
      borderRadius: "8px",
    },
    headerTitle: {
      fontSize: "2rem",
      fontWeight: "bold",
      color: "#333333",
    },
    headerSubtitle: {
      color: "#666666",
    },
    socialButtonsBlockButton: {
      border: "1px solid #dddddd",
      "&:hover": {
        backgroundColor: "#f5f5f5",
      },
    },
    socialButtonsBlockButtonText: {
      color: "#555555",
    },
    dividerLine: {
      backgroundColor: "#eeeeee",
    },
    dividerText: {
      color: "#999999",
    },
    formFieldLabel: {
      color: "#444444",
    },
    formFieldInput: {
      border: "1px solid #cccccc",
      borderRadius: "4px",
      "&:focus": {
        borderColor: "#000000",
      },
    },
    footerActionLink: {
      color: "#000000",
      "&:hover": {
        color: "#333333",
      },
    },
  },
  variables: {
    colorPrimary: "#000000",
    colorText: "#333333",
    colorBackground: "#ffffff",
    colorInputBackground: "#ffffff",
    colorInputText: "#333333",
  },
};
