/* SliderCards.scss
   Custom responsive styles for the card slider component.
   Desktop view sizes remain unchanged; mobile & tablet scale smoothly.
*/

.slider-cards {
  /* Let <PERSON><PERSON> Slider manage layout & spacing */
  width: 100%;
  height: auto;
  overflow: hidden;
}

.card-slide {
  flex: 0 0 auto;
  display: flex;
  align-items: stretch;
  /* Width & spacing handled by <PERSON><PERSON>lider */
}

.slider-card {
  display: flex;
  flex-direction: column;
  width: 100%;
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.slider-card__img {
  width: 100%;
  object-fit: cover;
  flex-shrink: 0;
}

.slider-card__body {
  flex: 1 1 auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Typography */
.slider-card__title {
  font-size: 1.1rem;
  font-weight: 600;
}

.slider-card__desc {
  font-size: 0.95rem;
  color: #555;
}

.slider-card__price {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1a1a1a;
}

/* Tablet */
@media (max-width: 1024px) {
  .card-slide {
    /* width handled by <PERSON><PERSON> Slider */
    /* gap handled by Keen Slider spacing */
  }
  .slider-card {
    aspect-ratio: 3 / 4;
  }
}

/* Small tablets & large phones */
@media (max-width: 768px) {
  .slider-cards {
    /* gap handled by Keen Slider spacing */
  }
  .card-slide {
    /* width handled by Keen Slider */
  }
  .slider-card {
    aspect-ratio: 3 / 4;
  }
}

/* Phones */
@media (max-width: 480px) {
  .slider-cards {
    /* gap handled by Keen Slider spacing */
  }
  .card-slide {
    /* width handled by Keen Slider */
  }
  .slider-card {
    aspect-ratio: 3 / 4;
  }
}
