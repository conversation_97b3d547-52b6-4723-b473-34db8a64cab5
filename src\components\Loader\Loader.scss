.loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: #000;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  overflow: hidden;
  will-change: opacity, transform;
  backface-visibility: hidden;
  perspective: 1000px;

  .loader-counter {
    position: absolute;
    color: #fff;
    font-size: 8rem;
    font-family: "PPR";
    font-weight: 400;
    letter-spacing: -2px;
    z-index: 2;
    text-align: center;
    width: 100%;
    pointer-events: none;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .loader-text-container {
    position: absolute;
    z-index: 2;
    text-align: center;
    width: 100%;
    pointer-events: none;
  }

  .loader-text {
    color: #fff;
    font-size: 5rem;
    font-family: "PPS";
    letter-spacing: -1px;
    margin: 0;
    padding: 0;
    display: inline-block;
  }

  .loader-image {
    position: absolute;
    width: 400px;
    height: 500px;
    overflow: hidden;
    z-index: 1;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      filter: grayscale(100%);
      transition: filter 0.3s ease;
    }
  }
}
