import { render } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { describe, it, expect, vi } from 'vitest';
import { configureStore } from '@reduxjs/toolkit';
import App from './App';
import cartReducer from './features/cartSlice';
import { apiSlice } from './features/apiSlice';

// Mock the LenisProvider
vi.mock('./components/common/LenisProvider', () => ({
  default: ({ children }) => children,
}));

// Create a test store
const createTestStore = () => {
  return configureStore({
    reducer: {
      [apiSlice.reducerPath]: apiSlice.reducer,
      cart: cartReducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware().concat(apiSlice.middleware),
  });
};

const renderWithProviders = (component) => {
  const store = createTestStore();
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('App', () => {
  it('renders without crashing', () => {
    expect(() => renderWithProviders(<App />)).not.toThrow();
  });

  it('wraps content with ErrorBoundary', () => {
    const { container } = renderWithProviders(<App />);
    expect(container.firstChild).toBeTruthy();
  });
});
