import { describe, it, expect, beforeEach, vi } from 'vitest';
import cartReducer, {
  addToCart,
  removeFromCart,
  updateQuantity,
  clearCart,
  selectCartItems,
  selectCartCount,
  selectCartTotal
} from './cartSlice';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
global.localStorage = localStorageMock;

// Mock toast
vi.mock('react-toastify', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe('cartSlice', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  const initialState = {
    cartItems: [],
    cartCount: 0,
    isCartUpdated: false,
  };

  const mockProduct = {
    id: 1,
    name: 'Test Product',
    price: '100',
    image: 'test.jpg',
  };

  it('should return the initial state', () => {
    expect(cartReducer(undefined, { type: 'unknown' })).toEqual(initialState);
  });

  it('should handle addToCart', () => {
    const action = addToCart({
      product: mockProduct,
      selectedSize: 'M',
      selectedColor: 'Red',
      quantity: 1,
    });

    const state = cartReducer(initialState, action);

    expect(state.cartItems).toHaveLength(1);
    expect(state.cartItems[0]).toEqual({
      ...mockProduct,
      selectedSize: 'M',
      selectedColor: 'Red',
      quantity: 1,
      price: 100,
    });
    expect(state.cartCount).toBe(1);
    expect(state.isCartUpdated).toBe(true);
  });

  it('should handle adding same product with different size/color', () => {
    let state = cartReducer(initialState, addToCart({
      product: mockProduct,
      selectedSize: 'M',
      selectedColor: 'Red',
      quantity: 1,
    }));

    state = cartReducer(state, addToCart({
      product: mockProduct,
      selectedSize: 'L',
      selectedColor: 'Red',
      quantity: 1,
    }));

    expect(state.cartItems).toHaveLength(2);
    expect(state.cartCount).toBe(2);
  });

  it('should handle adding same product with same size/color (increase quantity)', () => {
    let state = cartReducer(initialState, addToCart({
      product: mockProduct,
      selectedSize: 'M',
      selectedColor: 'Red',
      quantity: 1,
    }));

    state = cartReducer(state, addToCart({
      product: mockProduct,
      selectedSize: 'M',
      selectedColor: 'Red',
      quantity: 2,
    }));

    expect(state.cartItems).toHaveLength(1);
    expect(state.cartItems[0].quantity).toBe(3);
    expect(state.cartCount).toBe(3);
  });

  it('should handle removeFromCart', () => {
    const stateWithItem = {
      cartItems: [{
        ...mockProduct,
        selectedSize: 'M',
        selectedColor: 'Red',
        quantity: 1,
        price: 100,
      }],
      cartCount: 1,
      isCartUpdated: false,
    };

    const action = removeFromCart({
      itemId: 1,
      selectedSize: 'M',
      selectedColor: 'Red',
    });

    const state = cartReducer(stateWithItem, action);

    expect(state.cartItems).toHaveLength(0);
    expect(state.cartCount).toBe(0);
  });

  it('should handle updateQuantity', () => {
    const stateWithItem = {
      cartItems: [{
        ...mockProduct,
        selectedSize: 'M',
        selectedColor: 'Red',
        quantity: 1,
        price: 100,
      }],
      cartCount: 1,
      isCartUpdated: false,
    };

    const action = updateQuantity({
      itemId: 1,
      selectedSize: 'M',
      selectedColor: 'Red',
      newQuantity: 3,
    });

    const state = cartReducer(stateWithItem, action);

    expect(state.cartItems[0].quantity).toBe(3);
    expect(state.cartCount).toBe(3);
  });

  it('should handle clearCart', () => {
    const stateWithItems = {
      cartItems: [mockProduct],
      cartCount: 1,
      isCartUpdated: false,
    };

    const state = cartReducer(stateWithItems, clearCart());

    expect(state.cartItems).toHaveLength(0);
    expect(state.cartCount).toBe(0);
  });

  // Test selectors
  it('should select cart items', () => {
    const state = {
      cart: {
        cartItems: [mockProduct],
        cartCount: 1,
        isCartUpdated: false,
      },
    };

    expect(selectCartItems(state)).toEqual([mockProduct]);
  });

  it('should select cart count', () => {
    const state = {
      cart: {
        cartItems: [],
        cartCount: 5,
        isCartUpdated: false,
      },
    };

    expect(selectCartCount(state)).toBe(5);
  });

  it('should calculate cart total', () => {
    const state = {
      cart: {
        cartItems: [
          { price: 100, quantity: 2 },
          { price: 50, quantity: 1 },
        ],
        cartCount: 3,
        isCartUpdated: false,
      },
    };

    expect(selectCartTotal(state)).toBe(250); // (100 * 2) + (50 * 1)
  });
});
