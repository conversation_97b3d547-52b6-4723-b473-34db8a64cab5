import "@testing-library/jest-dom";
import { vi } from "vitest";

// Mock environment variables
vi.mock("import.meta", () => ({
  env: {
    VITE_CLERK_PUBLISHABLE_KEY: "test-key",
  },
}));

// Mock Clerk
vi.mock("@clerk/clerk-react", () => ({
  ClerkProvider: ({ children }) => children,
  useAuth: () => ({
    isSignedIn: false,
    userId: null,
  }),
  UserButton: () => null,
  SignIn: () => null,
  SignUp: () => null,
}));

// Mock GSAP
vi.mock("gsap", () => ({
  default: {
    set: vi.fn(),
    to: vi.fn(),
    fromTo: vi.fn(),
    timeline: vi.fn(() => ({
      to: vi.fn().mockReturnThis(),
      fromTo: vi.fn().mockReturnThis(),
      set: vi.fn().mockReturnThis(),
    })),
    registerPlugin: vi.fn(),
  },
}));

// Mock GSAP ScrollTrigger
vi.mock("gsap/ScrollTrigger", () => ({
  default: {},
  ScrollTrigger: {},
}));

// Mock Lenis
vi.mock("lenis", () => ({
  default: vi.fn(() => ({
    on: vi.fn(),
    off: vi.fn(),
    destroy: vi.fn(),
  })),
}));

// Mock react-router-dom
vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useNavigate: () => vi.fn(),
    useLocation: () => ({ pathname: "/" }),
  };
});
